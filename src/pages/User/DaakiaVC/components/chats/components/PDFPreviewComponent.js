import { useState } from 'react';
import { Document, Page } from 'react-pdf';
import { MdOutlineZoomIn, MdOutlineZoomOut, MdZoomInMap } from 'react-icons/md';

/**
 * Reusable PDF Preview Component with Controls
 * Handles PDF rendering, page navigation, and zoom functionality
 */
export function PDFPreviewComponent({ 
  fileUrl, 
  onLoadSuccess,
  className = "lk-chat-pdf-preview-container"
}) {
  // PDF-specific state
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfScale, setPdfScale] = useState(1.0);

  // PDF-specific functions
  const handleDocumentLoadSuccess = ({ numPages: totalPages }) => {
    setNumPages(totalPages);
    setPageNumber(1);
    // Call parent callback if provided
    if (onLoadSuccess) {
      onLoadSuccess({ numPages: totalPages });
    }
  };

  // PDF zoom controls
  const pdfZoomIn = () => setPdfScale((prev) => Math.min(prev + 0.2, 3));
  const pdfZoomOut = () => setPdfScale((prev) => Math.max(prev - 0.2, 0.5));
  const resetPdfZoom = () => setPdfScale(1.0);

  // PDF navigation
  const goToPrevPage = () => setPageNumber((prev) => Math.max(prev - 1, 1));
  const goToNextPage = () => setPageNumber((prev) => Math.min(prev + 1, numPages || 1));

  return (
    <div className={className} style={{ textAlign: "center" }}>
      <Document
        file={fileUrl}
        onLoadSuccess={handleDocumentLoadSuccess}
        loading={<div>Loading PDF...</div>}
        error={
          <div>
            Failed to load PDF. 
            <a href={fileUrl} target="_blank" rel="noopener noreferrer">
              Open in new tab
            </a>
          </div>
        }
        className="lk-chat-pdf-document"
      >
        <Page
          pageNumber={pageNumber}
          scale={pdfScale}
          className="lk-chat-pdf-page"
          renderAnnotationLayer={false}
          renderTextLayer={false}
        />
      </Document>

      {/* PDF Controls */}
      <div 
        className="lk-chat-pdf-controls" 
        style={{
          marginTop: "10px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          gap: "10px",
          flexWrap: "wrap"
        }}
      >
        {/* Page Navigation */}
        {numPages && numPages > 1 && (
          <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
            <button
              onClick={goToPrevPage}
              disabled={pageNumber <= 1}
              style={{
                padding: "5px 10px",
                border: "1px solid #ccc",
                background: pageNumber <= 1 ? "#f5f5f5" : "#fff",
                cursor: pageNumber <= 1 ? "not-allowed" : "pointer"
              }}
            >
              Previous
            </button>
            <span style={{ margin: "0 10px" }}>
              Page {pageNumber} of {numPages}
            </span>
            <button
              onClick={goToNextPage}
              disabled={pageNumber >= numPages}
              style={{
                padding: "5px 10px",
                border: "1px solid #ccc",
                background: pageNumber >= numPages ? "#f5f5f5" : "#fff",
                cursor: pageNumber >= numPages ? "not-allowed" : "pointer"
              }}
            >
              Next
            </button>
          </div>
        )}

        {/* PDF Zoom Controls */}
        <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
          <MdOutlineZoomOut
            onClick={pdfZoomOut}
            className="attatchment-zoom-out"
            style={{ cursor: "pointer", fontSize: "20px" }}
          />
          <span style={{ margin: "0 5px", fontSize: "14px" }}>
            {Math.round(pdfScale * 100)}%
          </span>
          <MdOutlineZoomIn
            onClick={pdfZoomIn}
            className="attatchment-zoom-in"
            style={{ cursor: "pointer", fontSize: "20px" }}
          />
          <MdZoomInMap
            onClick={resetPdfZoom}
            className="attatchment-zoom-reset"
            style={{ cursor: "pointer", fontSize: "20px", marginLeft: "5px" }}
          />
        </div>
      </div>
    </div>
  );
}

export default PDFPreviewComponent;
