import { useState } from 'react';
import { Document, Page } from 'react-pdf';
import { MdOutlineZoomIn, MdOutlineZoomOut, MdZoomInMap, MdNavigateBefore, MdNavigateNext } from 'react-icons/md';

/**
 * Reusable PDF Preview Component with Controls
 * Handles PDF rendering, page navigation, and zoom functionality in a centered frame
 */
export function PDFPreviewComponent({
  fileUrl,
  onLoadSuccess,
  className = "lk-chat-pdf-preview-container"
}) {
  // PDF-specific state
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfScale, setPdfScale] = useState(1.0);

  // PDF-specific functions
  const handleDocumentLoadSuccess = ({ numPages: totalPages }) => {
    setNumPages(totalPages);
    setPageNumber(1);
    // Call parent callback if provided
    if (onLoadSuccess) {
      onLoadSuccess({ numPages: totalPages });
    }
  };

  // PDF zoom controls
  const pdfZoomIn = () => setPdfScale((prev) => Math.min(prev + 0.2, 3));
  const pdfZoomOut = () => setPdfScale((prev) => Math.max(prev - 0.2, 0.5));
  const resetPdfZoom = () => setPdfScale(1.0);

  // PDF navigation
  const goToPrevPage = () => setPageNumber((prev) => Math.max(prev - 1, 1));
  const goToNextPage = () => setPageNumber((prev) => Math.min(prev + 1, numPages || 1));

  const frameStyles = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '500px',
    maxHeight: '80vh',
    width: '100%',
    maxWidth: '900px',
    margin: '0 auto',
    backgroundColor: 'transparent',
    borderRadius: '12px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e1e5e9',
    overflow: 'hidden',
    position: 'relative'
  };

  const headerStyles = {
    width: '100%',
    padding: '8px 16px',
    backgroundColor: '#f8f9fa',
    borderBottom: '1px solid #e1e5e9',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexShrink: 0
  };

  const contentStyles = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '10px',
    overflow: 'auto',
    width: '100%'
  };

  const controlsStyles = {
    width: '100%',
    padding: '8px 16px',
    backgroundColor: '#f8f9fa',
    borderTop: '1px solid #e1e5e9',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '12px',
    flexWrap: 'wrap',
    flexShrink: 0
  };

  const buttonStyles = {
    padding: '8px 16px',
    border: '1px solid #d0d7de',
    borderRadius: '6px',
    backgroundColor: '#ffffff',
    color: '#24292f',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '6px'
  };

  const disabledButtonStyles = {
    ...buttonStyles,
    backgroundColor: '#f6f8fa',
    color: '#8c959f',
    cursor: 'not-allowed',
    border: '1px solid #d1d9e0'
  };

  const iconButtonStyles = {
    padding: '8px',
    border: '1px solid #d0d7de',
    borderRadius: '6px',
    backgroundColor: '#ffffff',
    color: '#24292f',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.2s ease',
    width: '36px',
    height: '36px'
  };

  return (
    <div className={className}>
      <div style={frameStyles}>
        {/* Header */}
        <div style={headerStyles}>
          <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#24292f' }}>
            PDF Preview
          </h3>
          {numPages && (
            <span style={{ fontSize: '14px', color: '#656d76' }}>
              Page {pageNumber} of {numPages}
            </span>
          )}
        </div>

        {/* PDF Content */}
        <div style={contentStyles}>
          <Document
            file={fileUrl}
            onLoadSuccess={handleDocumentLoadSuccess}
            loading={
              <div style={{
                padding: '40px',
                color: '#656d76',
                fontSize: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                Loading PDF...
              </div>
            }
            error={
              <div style={{
                padding: '40px',
                color: '#d1242f',
                fontSize: '16px',
                textAlign: 'center'
              }}>
                <div style={{ marginBottom: '12px' }}>Failed to load PDF</div>
                <a
                  href={fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: '#0969da',
                    textDecoration: 'none',
                    fontSize: '14px'
                  }}
                >
                  Open in new tab
                </a>
              </div>
            }
            className="lk-chat-pdf-document"
          >
            <Page
              pageNumber={pageNumber}
              scale={pdfScale}
              renderAnnotationLayer={false}
              renderTextLayer={false}
              style={{
                width: '100%',
                height: '100%',
                margin: '0',
                padding: '0',
              }}
            />
          </Document>
        </div>

        {/* Controls Footer */}
        <div style={controlsStyles}>
          {/* Page Navigation */}
          {numPages && numPages > 1 && (
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <button
                onClick={goToPrevPage}
                disabled={pageNumber <= 1}
                style={pageNumber <= 1 ? disabledButtonStyles : buttonStyles}
                onMouseEnter={(e) => {
                  if (pageNumber > 1) {
                    e.target.style.backgroundColor = '#f3f4f6';
                    e.target.style.borderColor = '#9ca3af';
                  }
                }}
                onMouseLeave={(e) => {
                  if (pageNumber > 1) {
                    e.target.style.backgroundColor = '#ffffff';
                    e.target.style.borderColor = '#d0d7de';
                  }
                }}
              >
                <MdNavigateBefore size={16} />
                Previous
              </button>

              <button
                onClick={goToNextPage}
                disabled={pageNumber >= numPages}
                style={pageNumber >= numPages ? disabledButtonStyles : buttonStyles}
                onMouseEnter={(e) => {
                  if (pageNumber < numPages) {
                    e.target.style.backgroundColor = '#f3f4f6';
                    e.target.style.borderColor = '#9ca3af';
                  }
                }}
                onMouseLeave={(e) => {
                  if (pageNumber < numPages) {
                    e.target.style.backgroundColor = '#ffffff';
                    e.target.style.borderColor = '#d0d7de';
                  }
                }}
              >
                Next
                <MdNavigateNext size={16} />
              </button>
            </div>
          )}

          {/* Zoom Controls */}
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <button
              onClick={pdfZoomOut}
              style={iconButtonStyles}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#f3f4f6';
                e.target.style.borderColor = '#9ca3af';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = '#ffffff';
                e.target.style.borderColor = '#d0d7de';
              }}
              title="Zoom Out"
            >
              <MdOutlineZoomOut size={18} />
            </button>

            <span style={{
              minWidth: '60px',
              textAlign: 'center',
              fontSize: '14px',
              fontWeight: '500',
              color: '#24292f'
            }}>
              {Math.round(pdfScale * 100)}%
            </span>

            <button
              onClick={pdfZoomIn}
              style={iconButtonStyles}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#f3f4f6';
                e.target.style.borderColor = '#9ca3af';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = '#ffffff';
                e.target.style.borderColor = '#d0d7de';
              }}
              title="Zoom In"
            >
              <MdOutlineZoomIn size={18} />
            </button>

            <button
              onClick={resetPdfZoom}
              style={iconButtonStyles}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#f3f4f6';
                e.target.style.borderColor = '#9ca3af';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = '#ffffff';
                e.target.style.borderColor = '#d0d7de';
              }}
              title="Reset Zoom"
            >
              <MdZoomInMap size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PDFPreviewComponent;
